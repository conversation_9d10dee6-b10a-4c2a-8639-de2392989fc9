package exchange.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import exchange.common.component.CustomTransactionManager;
import exchange.common.config.SpotOrderApiConfig;
import exchange.common.constant.CommonConstants;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.entity.ApiInfo;
import exchange.common.entity.CoverOrderConfig;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.http.HttpManager;
import exchange.common.model.response.SpotCoverOrderApiResponse;
import exchange.common.repos.PosCoverOrderRepository;
import exchange.common.repos.PosUnCoverOrderRepository;
import exchange.common.service.CoverOrderConfigService;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.SymbolService;
import exchange.common.util.JsonUtil;
import exchange.pos.entity.PosCoverOrder;
import exchange.pos.entity.PosOrder;
import exchange.pos.entity.PosUnCoverOrder;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotTrade;
import exchange.spot.service.SpotOrderService;
import exchange.spot.service.SpotTradeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class PosNidtCoverOrderService {
    @Autowired
    PosOrderService posOrderService;

    @Autowired
    SymbolService symbolService;

    @Value("${exchange-pos.base-trade.coinbook.api-nidt-order}")
    private String path;

    @Value("${exchange-pos.base-trade.coinbook.api-host}")
    private String host;

    @Value("${exchange-pos.base-trade.coinbook.secret-buy}")
    private String secretBuy;

    @Value("${exchange-pos.base-trade.coinbook.api-key-buy}")
    private String apiKeyBuy;
    
    @Value("${exchange-pos.base-trade.coinbook.secret-sell}")
    private String secretSell;

    @Value("${exchange-pos.base-trade.coinbook.api-key-sell}")
    private String apiKeySell;

    @Autowired
    private HttpManager<SpotCoverOrderApiResponse> ordersHttpManager;
    @Autowired
    CoverOrderConfigService coverOrderConfigService;

    @Autowired
    SpotOrderApiConfig spotOrderApiConfig;

    @Autowired
    CurrencyPairConfigService currencyPairConfigService;

    @Autowired
    PosCoverOrderRepository posCoverOrderRepository;

    @Autowired
    PosUnCoverOrderRepository posUnCoverOrderRepository;

    @Autowired
    PosCoverOrderService posCoverOrderService;

    @Autowired
    private CustomTransactionManager customTransactionManager;

    private final int NUMBER = 0;

    private final int SIZE = 200;

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void sendNIDTOrder() throws Exception {

        //generate api info by config
    	ApiInfo marketMakerApiInfo = new ApiInfo();
    	
        ApiInfo marketMakerApiInfoBuy = new ApiInfo();
        marketMakerApiInfoBuy.setSecret(secretBuy);
        marketMakerApiInfoBuy.setApiKey(apiKeyBuy);
        
        ApiInfo marketMakerApiInfoSell = new ApiInfo();
        marketMakerApiInfoSell.setSecret(secretSell);
        marketMakerApiInfoSell.setApiKey(apiKeySell);
        

        //根据条件查找Symbol
        Symbol symbol = symbolService.findByCondition(TradeType.POS, CurrencyPair.NIDT_JPY); //10

        //根据条件查找Symbol
        Symbol exchangeSymbol = symbolService.findByCondition(TradeType.SPOT, CurrencyPair.NIDT_JPY); //5

        //根据symbolId获取PosOrder列表
        List<PosOrder> list = posOrderService.getOrderList(symbol.getId(),false,NUMBER,SIZE);

        //根据symbolId获取CoverOrderConfig
        CoverOrderConfig config = coverOrderConfigService.findOne(symbol.getId(), Exchange.COINBOOK, null);

        //获取注文数量设定百分比 并将其除以100转换为小数形式。（100%则转换成1）
        BigDecimal sendAmountPercent = config.getOrderAmountPercent().divide(new BigDecimal("100"));

        //获取CurrencyPairConfig
        CurrencyPairConfig currencyPair = currencyPairConfigService.findByCondition(TradeType.POS,CurrencyPair.NIDT_JPY);

        //获取pos滑点百分比
        BigDecimal posSlippagePercent = currencyPair.getPosSlippagePercent().divide(new BigDecimal("100"));

        log.info("order amount percent:{} , pos slippage percent:{}",sendAmountPercent,currencyPair.getPosSlippagePercent());
        // 处理每个POS订单
        for (PosOrder order : list) {

            // 计算实际发送数量（按配置百分比）
            BigDecimal sendAmount = order.getAmount().multiply(sendAmountPercent);

            // 根据买卖方向计算发送价格（考虑滑点）
            BigDecimal originPrice = order.getMmPrice();
            //after calculate price
            BigDecimal sendPrice;
            if(OrderSide.BUY.equals(order.getOrderSide())){
                //case buy: high price
                sendPrice = originPrice.add(posSlippagePercent.multiply(originPrice));
                marketMakerApiInfo = marketMakerApiInfoBuy;
            }else{
                //case sell: low price
                sendPrice = originPrice.subtract(posSlippagePercent.multiply(originPrice));
                marketMakerApiInfo = marketMakerApiInfoSell;
            }

            log.info("pre send, pos order id :{},origin mm price:{},send amount:{},remaining amount:{} , send price:{}"
            ,order.getId(),originPrice,order.getAmount(),sendAmount,sendPrice);
            // 发送订单并获取响应
            SpotCoverOrderApiResponse response = send(order.getOrderSide(),marketMakerApiInfo,host+path,sendPrice,sendAmount,exchangeSymbol,order);
            if(ObjectUtils.isEmpty(response)) {
                continue;
            }
            //generate pos cover order 生成并保存覆盖订单记录
            PosCoverOrder posCoverOrder = generatePosCoverOrder(order,response);
            // 如果发送比例小于100%，生成未覆盖订单记录
            if(sendAmountPercent.compareTo(BigDecimal.ONE) < 0){
                //generate pos uncover order
                //uncoverAmount  amount
                BigDecimal uncoverAmount  = order.getAmount().subtract(response.getAmount());
                PosUnCoverOrder posUnCoverOrder =  generatePosUnCoverOrder(order,"UnCoverOrder",uncoverAmount,exchangeSymbol);
                posUnCoverOrderRepository.save(posUnCoverOrder);
                posCoverOrder.setAmountManualNidt(uncoverAmount);
                posCoverOrder.setRemainingAmountManualNidt(uncoverAmount);
            }
            // 保存订单状态
            posCoverOrderRepository.save(posCoverOrder);
            log.info("response order :{} ",JsonUtil.encode(response));

            //set order covered true
            order.setCovered(true);
            order.setUpdatedAt(new Date());
            posOrderService.save(order);

        }


    }

    public SpotCoverOrderApiResponse send(OrderSide side,ApiInfo apiInfo,String url,BigDecimal price,BigDecimal amount,Symbol symbol,PosOrder order) throws Exception {
        BigDecimal amountScaled = symbol.getCurrencyPair().getScaledAmount(amount,symbol.getCurrencyPair().getPosBasePrecision(),RoundingMode.HALF_UP);
        TreeMap treeMap = new TreeMap<>();
        treeMap.put("symbolId",symbol.getId());//exchange symbol
        treeMap.put("orderSide",side);
        treeMap.put("orderType", OrderType.LIMIT);
        if(side.isSell()){
            treeMap.put("price",symbol.getCurrencyPair().getScaledPrice(price,RoundingMode.DOWN));
        } else {
            treeMap.put("price",symbol.getCurrencyPair().getScaledPrice(price,RoundingMode.UP));
        }
        treeMap.put("amount",amountScaled);
        SpotCoverOrderApiResponse response;
        try {
             response = ordersHttpManager.doPostJson(url,
                    treeMap,
                    spotOrderApiConfig.createHeaderMapPostOrPut(treeMap, apiInfo),
                    null,
                    SpotCoverOrderApiResponse.class);
            log.info("order response info:{}",response);
            return response;
        } catch (Exception e) {
            log.warn("pos nidt cover order response error :{},order:{}",e.getMessage(),JsonUtil.encode(order));
            PosUnCoverOrder posUnCoverOrder =  generatePosUnCoverOrder(order,"cb api send error",order.getAmount(),symbol);
            posUnCoverOrderRepository.save(posUnCoverOrder);

            //generate pos cover order
            PosCoverOrder posCoverOrder = generatePosCoverOrderError(order,amountScaled, treeMap.get("price").toString(),symbol);
            posCoverOrderRepository.save(posCoverOrder);
            order.setCovered(true);
            order.setUpdatedAt(new Date());
            posOrderService.save(order);
            log.info("pos nidt cover order save success");
            return null;
        }
    }

    public PosCoverOrder generatePosCoverOrder(PosOrder order, SpotCoverOrderApiResponse response){
        PosCoverOrder posCoverOrder = new PosCoverOrder();
        posCoverOrder.setSymbolId(response.getSymbolId());
        posCoverOrder.setOrderId(order.getId());
        posCoverOrder.setOrderSide(OrderSide.valueOfName(response.getOrderSide()));
        posCoverOrder.setOrderType(OrderType.valueOf(response.getOrderType()));
        posCoverOrder.setPrice(response.getPrice());
        posCoverOrder.setAveragePrice(response.getAveragePrice());
        posCoverOrder.setAmount(response.getAmount());
        posCoverOrder.setRemainingAmount(response.getRemainingAmount());
        posCoverOrder.setExchange(Exchange.COINBOOK);
        posCoverOrder.setCreatedAt(new Date());
        posCoverOrder.setExchangeOrderId(response.getId().toString());
        posCoverOrder.setFee(BigDecimal.ZERO);
        posCoverOrder.setStrategy("GTC");
        posCoverOrder.setOrderStatus(OrderStatus.valueOf(response.getOrderStatus()));
        return posCoverOrder;
    }
    
    public PosCoverOrder generatePosCoverOrderError(PosOrder order, BigDecimal amountScaled, String price, Symbol symbol){
      PosCoverOrder posCoverOrder = new PosCoverOrder();
      posCoverOrder.setSymbolId(symbol.getId());
      posCoverOrder.setOrderId(order.getId());
      posCoverOrder.setOrderSide(order.getOrderSide());
      posCoverOrder.setOrderType(order.getOrderType());
      posCoverOrder.setPrice(new BigDecimal(price));
      posCoverOrder.setAveragePrice(BigDecimal.ZERO);
      posCoverOrder.setAmount(amountScaled);
      posCoverOrder.setRemainingAmount(amountScaled);
      posCoverOrder.setExchange(Exchange.COINBOOK);
      posCoverOrder.setCreatedAt(new Date());
      posCoverOrder.setUpdatedAt(new Date());;
      posCoverOrder.setExchangeOrderId("cb api send error");
      posCoverOrder.setFee(BigDecimal.ZERO);
      posCoverOrder.setStrategy("GTC");
      posCoverOrder.setOrderStatus(OrderStatus.CANCELED_UNFILLED);
      posCoverOrder.setAmountManualNidt(order.getAmount());
      posCoverOrder.setRemainingAmountManualNidt(order.getAmount());
      return posCoverOrder;
  }

    public PosUnCoverOrder generatePosUnCoverOrder(PosOrder order,
        String remark,BigDecimal uncoverAmount,Symbol exchangeSymbol){
        PosUnCoverOrder posUnCoverOrder = new PosUnCoverOrder();
        posUnCoverOrder.setSymbolId(exchangeSymbol.getId());
        posUnCoverOrder.setOrderId(order.getId());
        posUnCoverOrder.setOrderSide(order.getOrderSide());
        posUnCoverOrder.setOrderType(OrderType.LIMIT);
        posUnCoverOrder.setPrice(order.getPrice());
        posUnCoverOrder.setAmount(uncoverAmount);
        posUnCoverOrder.setExchange(Exchange.COINBOOK);
        posUnCoverOrder.setCreatedAt(new Date());
        posUnCoverOrder.setFee(BigDecimal.ZERO);
        posUnCoverOrder.setRemark(remark);
        return posUnCoverOrder;
    }

    public void posCoverOrderExecute(Symbol symbol) throws Exception {
        // 根据条件查询PosCoverOrder列表
        List<PosCoverOrder> list = posCoverOrderService.findByCondition(symbol.getId(),
            List.of(OrderStatus.WAITING, OrderStatus.UNFILLED,OrderStatus.PARTIALLY_FILLED), Exchange.COINBOOK, BigDecimal.ZERO,NUMBER,SIZE);
        for (PosCoverOrder posCoverOrder : list) {
            // 获取交易所订单ID
            String spotOrderId =  posCoverOrder.getExchangeOrderId();
            // 根据交易所订单ID查询SpotOrder列表
            List<SpotOrder> orderList = SpotOrderService.getBean(symbol)
                .findOrders(symbol, List.of(Long.valueOf(spotOrderId)), null, null).toList();
            if(!CollectionUtils.isEmpty(orderList)){
              // 获取SpotOrder
              SpotOrder spotOrder = orderList.get(0);
              // 执行自定义事务
              customTransactionManager.execute(entityManager -> {
                // 手数料計算
                // 根据SpotOrder的symbolId查询Symbol
                Symbol symbolRes = symbolService.findOne(spotOrder.getSymbolId());
                // 根据SpotOrder的symbolId和orderId查询SpotTrade列表
                List<SpotTrade> spotTrades = SpotTradeService.getBean(symbolRes).
                    findByOrderId(spotOrder.getSymbolId(), spotOrder.getId(), null);
                BigDecimal fee = BigDecimal.ZERO;
                if(ObjectUtils.isNotEmpty(spotTrades)) {
                  // 计算SpotTrade的手数料
                  fee = spotTrades.stream().map(SpotTrade::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                // 根据SpotOrder的symbolId和orderId查询SpotTrade列表
                List<SpotTrade> spotTradesHistory =
                    SpotTradeService.getBean(symbol)
                        .findFromHistory(
                            symbol,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            spotOrder.getId(),
                            null,
                            null,
                            null,
                            null,
                            null);
                if(ObjectUtils.isNotEmpty(spotTradesHistory)) {
                  // 计算SpotTrade的手数料
                  fee = spotTradesHistory.stream().map(SpotTrade::getFee).reduce(fee, BigDecimal::add);
                }
                // 更新PosCoverOrder的剩余数量、数量、手数料、平均价格、价格、订单状态
                posCoverOrder.setRemainingAmount(spotOrder.getRemainingAmount());
                posCoverOrder.setAmount(spotOrder.getAmount());
                posCoverOrder.setFee(fee);
                posCoverOrder.setAveragePrice(spotOrder.getAveragePrice());
                posCoverOrder.setPrice(spotOrder.getPrice());
                posCoverOrder.setOrderStatus(spotOrder.getOrderStatus());
                // 如果订单状态为CANCELED_UNFILLED或CANCELED_PARTIALLY_FILLED，则创建PosUnCoverOrder
                if(OrderStatus.CANCELED_UNFILLED.equals(spotOrder.getOrderStatus()) ||
                   OrderStatus.CANCELED_PARTIALLY_FILLED.equals(spotOrder.getOrderStatus())){
                    PosUnCoverOrder posUnCoverOrder = new PosUnCoverOrder();
                    posUnCoverOrder.setSymbolId(symbol.getId());
                    posUnCoverOrder.setOrderId(posCoverOrder.getOrderId());
                    posUnCoverOrder.setOrderSide(posCoverOrder.getOrderSide());
                    posUnCoverOrder.setOrderType(OrderType.LIMIT);
                    posUnCoverOrder.setPrice(posCoverOrder.getPrice());
                    posUnCoverOrder.setAmount(spotOrder.getRemainingAmount());
                    posUnCoverOrder.setExchange(Exchange.COINBOOK);
                    posUnCoverOrder.setCreatedAt(new Date());
                    posUnCoverOrder.setFee(BigDecimal.ZERO);
                    posUnCoverOrder.setRemark(spotOrder.getOrderStatus().name());
                    posUnCoverOrderRepository.save(posUnCoverOrder);
                    // 更新PosCoverOrder的手数料
                    posCoverOrder.setAmountManualNidt(posCoverOrder.getAmountManualNidt().add(spotOrder.getRemainingAmount()));
                    posCoverOrder.setRemainingAmountManualNidt(posCoverOrder.getRemainingAmountManualNidt().add(spotOrder.getRemainingAmount()));
                }
                // 保存PosCoverOrder
                posCoverOrderService.save(posCoverOrder,entityManager);

            });
            // 记录日志
            log.info("orderId:{};userId:{};OrderStatus:{}", spotOrder.getId(),
                spotOrder.getUserId(),
                spotOrder.getOrderStatus());
            }
        }
    }
}
